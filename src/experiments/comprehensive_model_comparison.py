"""
深度学习模型全面对比分析实验
对比分析CNN、RNN、注意力机制等不同架构的性能表现
包含：Baseline-BiGRU、DPTAM-BiGRU、ASB-DPTAM-BiGRU、CNN、CNN-BiGRU、BiLSTM、CNN-BiLSTM
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.models.dptam_bigru_model import DPTAMBiGRUModel
from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.models.cnn_model import CNNModel
from src.models.cnn_bigru_model import CNNBiGRUModel
from src.models.bilstm_model import BiLSTMModel
from src.models.cnn_bilstm_model import CNNBiLSTMModel
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, ASB_DPTAM_BIGRU_CONFIG,
    CNN_CONFIG, CNN_BIGRU_CONFIG, BILSTM_CONFIG, CNN_BILSTM_CONFIG,
    COMPARISON_EXPERIMENT_CONFIG, setup_matplotlib, setup_training_session, get_current_paths
)


class ComprehensiveModelComparator:
    """深度学习模型全面对比分析实验类"""
    
    def __init__(self):
        """初始化对比实验"""
        self.models = {}
        self.results = {}
        self.histories = {}
        self.data_loader = None
        self.preprocessor = None
        
        # 设置matplotlib和训练会话
        setup_matplotlib()
        setup_training_session()

        print("=" * 80)
        print("深度学习模型全面对比分析实验")
        print("=" * 80)
        print("实验目标:")
        print("• 对比CNN、RNN、注意力机制等不同架构的性能")
        print("• 验证各种融合策略的有效性")
        print("• 分析不同模型的参数效率和计算复杂度")
        print("• 为风电功率预测提供最优模型选择建议")
        print("=" * 80)

        # 模型配置映射
        self.model_configs = {
            'Baseline-BiGRU': (BiGRUModel, BIGRU_CONFIG),
            'DPTAM-BiGRU': (DPTAMBiGRUModel, DPTAM_BIGRU_CONFIG),
            'ASB-DPTAM-BiGRU': (ASBDPTAMBiGRUModel, ASB_DPTAM_BIGRU_CONFIG),
            'CNN': (CNNModel, CNN_CONFIG),
            'CNN-BiGRU': (CNNBiGRUModel, CNN_BIGRU_CONFIG),
            'BiLSTM': (BiLSTMModel, BILSTM_CONFIG),
            'CNN-BiLSTM': (CNNBiLSTMModel, CNN_BILSTM_CONFIG)
        }

        # 模型开关配置
        self.model_switches = {
            'Baseline-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_baseline_bigru'],
            'DPTAM-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_dptam_bigru'],
            'ASB-DPTAM-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_asb_dptam_bigru'],
            'CNN': COMPARISON_EXPERIMENT_CONFIG['enable_cnn'],
            'CNN-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bigru'],
            'BiLSTM': COMPARISON_EXPERIMENT_CONFIG['enable_bilstm'],
            'CNN-BiLSTM': COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bilstm']
        }

    def load_and_prepare_data(self) -> dict:
        """加载和预处理数据"""
        print("\n步骤1: 数据加载与预处理")
        print("-" * 40)
        
        # 数据加载
        self.data_loader = DataLoader()
        raw_data = self.data_loader.load_data()
        
        print(f"原始数据形状: {raw_data.shape}")

        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {raw_data[DATASET_CONFIG['target_column']].min():.4f}")
        print(f"  最大值: {raw_data[DATASET_CONFIG['target_column']].max():.4f}")
        print(f"  平均值: {raw_data[DATASET_CONFIG['target_column']].mean():.4f}")
        print(f"  标准差: {raw_data[DATASET_CONFIG['target_column']].std():.4f}")

        # 数据分析（跳过可视化以避免阻塞）
        self.data_loader.analyze_data()

        # 数据预处理
        self.preprocessor = DataPreprocessor()
        self.preprocessor.set_data(raw_data)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()

        # 保存预处理后的数据
        self.preprocessor.save_processed_data()

        # 准备训练数据
        processed_data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        processed_data['train_loader'] = train_loader
        processed_data['val_loader'] = val_loader
        processed_data['test_loader'] = test_loader

        # 保存测试数据加载器为类属性，用于后续可视化
        self.test_loader = test_loader
        
        print(f"数据准备完成，特征数量: {processed_data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        print(f"序列长度: {processed_data['sequence_length']}")
        print(f"训练集大小: {len(processed_data['train_loader'].dataset)}")
        print(f"验证集大小: {len(processed_data['val_loader'].dataset)}")
        print(f"测试集大小: {len(processed_data['test_loader'].dataset)}")

        return processed_data

    def train_model(self, model_name: str, data: dict) -> None:
        """训练指定模型"""
        if not self.model_switches.get(model_name, False):
            print(f"⏭️  跳过 {model_name} 模型（开关已关闭）")
            return

        print(f"\n训练 {model_name} 模型")
        print("-" * 40)
        
        model_class, config = self.model_configs[model_name]
        
        # 根据模型类型创建模型实例
        if model_name == 'Baseline-BiGRU':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                bigru_units=config['bigru_units'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate']
            )
        elif model_name == 'DPTAM-BiGRU':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                n_segment=config['n_segment'],
                dptam_kernel_size=config['dptam_kernel_size'],
                bigru_units=config['bigru_units'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate']
            )
        elif model_name == 'ASB-DPTAM-BiGRU':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                n_segment=config['n_segment'],
                dptam_kernel_size=config['dptam_kernel_size'],
                bigru_units=config['bigru_units'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate'],
                asb_adaptive_filter=config['asb_adaptive_filter']
            )
        elif model_name == 'CNN':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                conv_filters=config['conv_filters'],
                conv_kernel_sizes=config['conv_kernel_sizes'],
                pool_sizes=config['pool_sizes'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate']
            )
        elif model_name == 'CNN-BiGRU':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                conv_filters=config['conv_filters'],
                conv_kernel_sizes=config['conv_kernel_sizes'],
                pool_sizes=config['pool_sizes'],
                bigru_units=config['bigru_units'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate']
            )
        elif model_name == 'BiLSTM':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                bilstm_units=config['bilstm_units'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate']
            )
        elif model_name == 'CNN-BiLSTM':
            model = model_class(
                sequence_length=data['sequence_length'],
                n_features=data['n_features'],
                conv_filters=config['conv_filters'],
                conv_kernel_sizes=config['conv_kernel_sizes'],
                pool_sizes=config['pool_sizes'],
                bilstm_units=config['bilstm_units'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate']
            )
        
        # 设置标准化器
        model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories[model_name] = model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results[model_name] = model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 保存模型
        model_path = os.path.join(get_current_paths()['models'], f'{model_name.lower().replace("-", "_")}_model.pth')
        model.save_model(model_path)

        # 存储模型实例
        self.models[model_name] = model

        model_info = model.get_model_info()
        print(f"✅ {model_name} 训练完成")
        print(f"   参数量: {model_info['total_params']:,}")
        print(f"   测试RMSE: {self.results[model_name]['test']['RMSE']:.6f}")
        print(f"   模型已保存: {model_path}")

    def run_comparison_experiment(self) -> None:
        """运行完整的对比实验"""
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()
            
            # 2. 训练所有启用的模型
            for model_name in self.model_configs.keys():
                self.train_model(model_name, data)
            
            # 3. 分析对比结果
            self.analyze_comprehensive_comparison()
            
            # 4. 生成可视化结果
            self.generate_visualizations()

            # 5. 生成报告
            self.generate_comparison_report()

            print(f"\n🎉 深度学习模型全面对比实验完成！")
            print(f"📁 所有结果已保存到: {get_current_paths()['results_root']}")

            # 实验总结
            print(f"\n📋 实验总结:")
            for model_name, enabled in self.model_switches.items():
                status = "✅ 已训练" if enabled and model_name in self.results else "⏭️ 已跳过"
                print(f"{status} {model_name}")

        except Exception as e:
            print(f"\n❌ 实验过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def analyze_comprehensive_comparison(self) -> None:
        """分析全面对比结果"""
        print("\n步骤3: 深度学习模型全面对比分析")
        print("-" * 40)

        if not self.results:
            print("⚠️ 没有可用的模型结果进行分析")
            return

        # 按模型类型分组分析
        print(f"📊 性能对比分析:")

        # 基础模型性能
        print(f"\n🔹 基础模型性能:")
        for model_name in ['Baseline-BiGRU', 'BiLSTM', 'CNN']:
            if model_name in self.results:
                result = self.results[model_name]['test']
                print(f"  {model_name}:")
                print(f"    RMSE: {result['RMSE']:.6f}")
                print(f"    R^2:  {result['R2']:.6f}")
                print(f"    MAE:  {result['MAE']:.6f}")

        # 融合模型性能
        print(f"\n🔹 融合模型性能:")
        for model_name in ['DPTAM-BiGRU', 'ASB-DPTAM-BiGRU', 'CNN-BiGRU', 'CNN-BiLSTM']:
            if model_name in self.results:
                result = self.results[model_name]['test']
                print(f"  {model_name}:")
                print(f"    RMSE: {result['RMSE']:.6f}")
                print(f"    R^2:  {result['R2']:.6f}")
                print(f"    MAE:  {result['MAE']:.6f}")

        # 找出最佳模型
        best_model = min(self.results.keys(), key=lambda x: self.results[x]['test']['RMSE'])
        best_rmse = self.results[best_model]['test']['RMSE']

        print(f"\n🏆 最佳模型: {best_model}")
        print(f"   最佳RMSE: {best_rmse:.6f}")

        # 相对基线的改进分析
        if 'Baseline-BiGRU' in self.results:
            baseline_rmse = self.results['Baseline-BiGRU']['test']['RMSE']
            print(f"\n📈 相对基线BiGRU的改进:")

            for model_name, result in self.results.items():
                if model_name != 'Baseline-BiGRU':
                    model_rmse = result['test']['RMSE']
                    improvement = ((baseline_rmse - model_rmse) / baseline_rmse) * 100
                    status = "✅ 提升" if improvement > 0 else "❌ 下降"
                    print(f"  {model_name}: {improvement:+.2f}% {status}")

        # 参数效率分析
        print(f"\n⚙️ 参数效率分析:")
        for model_name in self.results.keys():
            if model_name in self.models:
                model_info = self.models[model_name].get_model_info()
                rmse = self.results[model_name]['test']['RMSE']
                params = model_info['total_params']
                efficiency = rmse * params / 1000000  # RMSE * 参数量(百万)
                print(f"  {model_name}:")
                print(f"    参数量: {params:,}")
                print(f"    RMSE: {rmse:.6f}")
                print(f"    效率指标: {efficiency:.3f}")

    def generate_visualizations(self) -> None:
        """生成可视化结果图表"""
        print("\n步骤4: 生成可视化结果")
        print("-" * 40)

        try:
            # 准备models_data格式的数据
            models_data = {}
            for model_name, model_wrapper in self.models.items():
                if model_name in self.results and hasattr(model_wrapper, 'history'):
                    models_data[model_name] = (self.results[model_name], model_wrapper.history)
                    print(f"📊 准备 {model_name} 可视化数据...")

            if models_data:
                # 使用auto_visualize生成标准可视化图表
                print("🎨 生成完整可视化套件...")
                generated_files = auto_visualize(
                    models_data=models_data,
                    experiment_name="Comprehensive_Model_Comparison",
                    save_path=get_current_paths()['figures']
                )

                # 生成性能对比图表
                self.create_comprehensive_comparison_plots()

                print("✅ 所有可视化结果已生成")
                print(f"📊 生成的图表文件数量: {len(generated_files.get('universal', [])) + len(generated_files.get('legacy', []))}")
            else:
                print("⚠️ 没有可用的模型数据，跳过可视化")

        except Exception as e:
            print(f"❌ 生成可视化时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_comprehensive_comparison_plots(self) -> None:
        """创建全面的性能对比图表"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            if not self.results:
                print("⚠️ 没有结果数据，跳过图表生成")
                return

            # 提取性能指标
            models = list(self.results.keys())
            rmse_values = [self.results[model]['test']['RMSE'] for model in models]
            r2_values = [self.results[model]['test']['R2'] for model in models]
            mae_values = [self.results[model]['test']['MAE'] for model in models]

            # 创建性能对比柱状图
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('深度学习模型全面性能对比', fontsize=16, fontweight='bold')

            # RMSE对比
            colors = plt.cm.Set3(np.linspace(0, 1, len(models)))
            bars1 = axes[0, 0].bar(models, rmse_values, color=colors)
            axes[0, 0].set_title('RMSE 对比', fontsize=14, fontweight='bold')
            axes[0, 0].set_ylabel('RMSE')
            axes[0, 0].tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars1, rmse_values):
                axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                               f'{value:.4f}', ha='center', va='bottom', fontsize=9)

            # R^2对比
            bars2 = axes[0, 1].bar(models, r2_values, color=colors)
            axes[0, 1].set_title('R^2 对比', fontsize=14, fontweight='bold')
            axes[0, 1].set_ylabel('R^2')
            axes[0, 1].tick_params(axis='x', rotation=45)

            for bar, value in zip(bars2, r2_values):
                axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                               f'{value:.4f}', ha='center', va='bottom', fontsize=9)

            # MAE对比
            bars3 = axes[1, 0].bar(models, mae_values, color=colors)
            axes[1, 0].set_title('MAE 对比', fontsize=14, fontweight='bold')
            axes[1, 0].set_ylabel('MAE')
            axes[1, 0].tick_params(axis='x', rotation=45)

            for bar, value in zip(bars3, mae_values):
                axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                               f'{value:.4f}', ha='center', va='bottom', fontsize=9)

            # 参数量对比
            param_counts = []
            for model_name in models:
                if model_name in self.models:
                    model_info = self.models[model_name].get_model_info()
                    param_counts.append(model_info['total_params'])
                else:
                    param_counts.append(0)

            bars4 = axes[1, 1].bar(models, param_counts, color=colors)
            axes[1, 1].set_title('参数量对比', fontsize=14, fontweight='bold')
            axes[1, 1].set_ylabel('参数数量')
            axes[1, 1].tick_params(axis='x', rotation=45)

            for bar, value in zip(bars4, param_counts):
                axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
                               f'{value:,}', ha='center', va='bottom', fontsize=9, rotation=90)

            plt.tight_layout()

            # 保存图表
            comparison_path = os.path.join(get_current_paths()['figures'], 'comprehensive_model_comparison.png')
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📊 全面性能对比图表已保存: {comparison_path}")

        except Exception as e:
            print(f"❌ 创建性能对比图表时发生错误: {str(e)}")

    def generate_comparison_report(self) -> None:
        """生成对比报告"""
        print("\n步骤5: 生成对比报告")
        print("-" * 40)

        if not self.results:
            print("⚠️ 没有结果数据，跳过报告生成")
            return

        # 找出最佳模型
        best_model = min(self.results.keys(), key=lambda x: self.results[x]['test']['RMSE'])
        best_rmse = self.results[best_model]['test']['RMSE']

        # 计算改进指标
        baseline_rmse = self.results.get('Baseline-BiGRU', {}).get('test', {}).get('RMSE', None)
        improvements = {}

        if baseline_rmse:
            for model_name in self.results.keys():
                if model_name != 'Baseline-BiGRU':
                    model_rmse = self.results[model_name]['test']['RMSE']
                    improvement = ((baseline_rmse - model_rmse) / baseline_rmse) * 100
                    improvements[model_name] = improvement

        # 生成报告内容
        report_content = f"""# 深度学习模型全面对比分析报告

## 实验概述
本实验对比了多种深度学习架构在风电功率预测任务上的性能表现：

### 参与对比的模型
"""

        for model_name, enabled in self.model_switches.items():
            status = "✅ 已训练" if enabled and model_name in self.results else "⏭️ 已跳过"
            if enabled and model_name in self.results:
                result = self.results[model_name]['test']
                report_content += f"- **{model_name}**: {status} (RMSE: {result['RMSE']:.6f})\n"
            else:
                report_content += f"- **{model_name}**: {status}\n"

        report_content += f"""
## 模型配置
- **序列长度**: {MODEL_CONFIG['sequence_length']}
- **训练轮数**: {MODEL_CONFIG['epochs']}
- **学习率**: {MODEL_CONFIG['learning_rate']}
- **批次大小**: {MODEL_CONFIG['batch_size']}

## 性能对比结果

### 测试集性能指标
| 模型 | RMSE | R^2 | MAE | 参数量 | 相对基线改进 |
|------|------|----|----|--------|-------------|
"""

        for model_name in self.results.keys():
            result = self.results[model_name]['test']
            if model_name in self.models:
                model_info = self.models[model_name].get_model_info()
                params = model_info['total_params']
            else:
                params = 'Unknown'

            improvement = improvements.get(model_name, 0.0)
            improvement_str = f"{improvement:+.2f}%" if model_name != 'Baseline-BiGRU' else "基线"

            report_content += f"| {model_name} | {result['RMSE']:.6f} | {result['R2']:.6f} | {result['MAE']:.6f} | {params:,} | {improvement_str} |\n"

        report_content += f"""
## 分析结论

### 🏆 最佳模型
**{best_model}** 获得最佳性能，测试集RMSE为 **{best_rmse:.6f}**

### 📊 模型类型分析

#### 基础模型表现
"""

        for model_name in ['Baseline-BiGRU', 'BiLSTM', 'CNN']:
            if model_name in self.results:
                result = self.results[model_name]['test']
                report_content += f"- **{model_name}**: RMSE {result['RMSE']:.6f}, 适合作为基线对比\n"

        report_content += f"""
#### 融合模型表现
"""

        for model_name in ['DPTAM-BiGRU', 'ASB-DPTAM-BiGRU', 'CNN-BiGRU', 'CNN-BiLSTM']:
            if model_name in self.results:
                result = self.results[model_name]['test']
                improvement = improvements.get(model_name, 0.0)
                status = "有效提升" if improvement > 0 else "效果有限"
                report_content += f"- **{model_name}**: RMSE {result['RMSE']:.6f}, 相对基线{improvement:+.2f}% ({status})\n"

        report_content += f"""
### 🔍 技术洞察

#### 架构优势分析
- **CNN模型**: 擅长提取局部特征模式，适合捕获短期时序特征
- **RNN模型**: 擅长建模长期时序依赖，适合捕获时序动态变化
- **注意力机制**: 能够自适应关注重要时间段，提升关键信息利用率
- **融合架构**: 结合不同架构优势，通常能获得更好的综合性能

#### 参数效率分析
"""

        for model_name in self.results.keys():
            if model_name in self.models:
                model_info = self.models[model_name].get_model_info()
                rmse = self.results[model_name]['test']['RMSE']
                params = model_info['total_params']
                efficiency = rmse * params / 1000000
                report_content += f"- **{model_name}**: {params:,}参数, 效率指标{efficiency:.3f}\n"

        report_content += f"""
### 💡 应用建议

#### 模型选择指导
1. **追求最高精度**: 选择 {best_model}
2. **平衡精度与效率**: 根据参数效率分析选择合适模型
3. **实时预测场景**: 优先考虑参数量较少的模型
4. **离线分析场景**: 可以选择复杂度较高但精度更好的模型

#### 进一步优化方向
- 超参数调优：针对最佳模型进行精细化参数调整
- 集成学习：结合多个表现良好的模型进行集成预测
- 特征工程：基于模型分析结果进行特征优化
- 架构改进：基于实验结果设计新的融合架构

---

**实验时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
**实验配置**: 深度学习模型全面对比
**数据集**: {DATASET_CONFIG['data_description']}
"""

        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'comprehensive_model_comparison_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ 深度学习模型全面对比报告已保存至: {report_path}")


def main():
    """主函数"""
    comparator = ComprehensiveModelComparator()
    comparator.run_comparison_experiment()


if __name__ == "__main__":
    main()
