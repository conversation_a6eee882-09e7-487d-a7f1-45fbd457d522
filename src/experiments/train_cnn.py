"""
CNN模型训练脚本
用于风电功率预测的卷积神经网络模型训练
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.cnn_model import CNNModel, CNN_CONFIG
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)


class CNNExperiment:
    """CNN模型训练实验类"""
    
    def __init__(self):
        """初始化实验"""
        # 设置时间戳训练会话
        self.training_paths = setup_training_session()
        print(f"🕒 CNN训练实验时间戳: {self.training_paths['timestamp']}")
        print(f"📁 结果保存路径: {self.training_paths['results_root']}")

        self.data_loader = DataLoader()
        self.preprocessor = DataPreprocessor()
        self.model = None
        self.history = None
        self.results = None

        # 设置matplotlib
        setup_matplotlib()

        print("✅ CNN模型训练实验初始化完成")

    def load_and_prepare_data(self) -> dict:
        """
        加载和准备数据
        
        Returns:
            准备好的训练数据
        """
        print("\n" + "=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)
        
        # 加载数据
        df = self.data_loader.load_data()
        
        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {df['Power (MW)'].min():.4f}")
        print(f"  最大值: {df['Power (MW)'].max():.4f}")
        print(f"  平均值: {df['Power (MW)'].mean():.4f}")
        print(f"  标准差: {df['Power (MW)'].std():.4f}")
        
        # 数据预处理
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()
        
        # 保存预处理后的数据
        self.preprocessor.save_processed_data()
        
        # 准备训练数据
        data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        data['train_loader'] = train_loader
        data['val_loader'] = val_loader
        data['test_loader'] = test_loader
        
        print(f"数据准备完成，特征数量: {data['n_features']}")
        print(f"序列长度: {data['sequence_length']}")
        print(f"训练集大小: {len(data['train_loader'].dataset)}")
        print(f"验证集大小: {len(data['val_loader'].dataset)}")
        print(f"测试集大小: {len(data['test_loader'].dataset)}")

        return data

    def create_and_train_model(self, data: dict) -> None:
        """
        创建和训练CNN模型
        
        Args:
            data: 准备好的训练数据
        """
        print("\n" + "=" * 60)
        print("步骤2: 创建和训练CNN模型")
        print("=" * 60)
        
        # 创建CNN模型
        self.model = CNNModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            conv_filters=CNN_CONFIG['conv_filters'],
            conv_kernel_sizes=CNN_CONFIG['conv_kernel_sizes'],
            pool_sizes=CNN_CONFIG['pool_sizes'],
            dense_units=CNN_CONFIG['dense_units'],
            dropout_rate=CNN_CONFIG['dropout_rate']
        )
        
        # 设置标准化器
        self.model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 获取模型信息
        model_info = self.model.get_model_info()
        print(f"CNN模型信息:")
        print(f"  模型名称: {model_info['model_name']}")
        print(f"  序列长度: {model_info['sequence_length']}")
        print(f"  特征数量: {model_info['n_features']}")
        print(f"  卷积滤波器: {model_info['conv_filters']}")
        print(f"  卷积核大小: {model_info['conv_kernel_sizes']}")
        print(f"  池化大小: {model_info['pool_sizes']}")
        print(f"  全连接层: {model_info['dense_units']}")
        print(f"  总参数量: {model_info['total_params']:,}")
        print(f"  可训练参数: {model_info['trainable_params']:,}")
        
        # 训练模型
        print("\n开始训练CNN模型...")
        self.history = self.model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        print("✅ CNN模型训练完成")

    def evaluate_model(self, data: dict) -> None:
        """
        评估模型性能
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤3: 模型性能评估")
        print("=" * 60)
        
        # 评估模型
        self.results = self.model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 打印评估结果
        print("CNN模型评估结果:")
        for dataset_name, metrics in self.results.items():
            print(f"\n{dataset_name.upper()}集:")
            for metric_name, value in metrics.items():
                print(f"  {metric_name}: {value:.6f}")

    def save_model_and_results(self) -> None:
        """保存模型和结果"""
        print("\n" + "=" * 60)
        print("步骤4: 保存模型和结果")
        print("=" * 60)
        
        # 保存模型
        model_path = os.path.join(get_current_paths()['models'], 'cnn_model.pth')
        self.model.save_model(model_path)
        print(f"✅ 模型已保存: {model_path}")
        
        # 保存训练历史
        history_path = os.path.join(get_current_paths()['models'], 'cnn_training_history.npy')
        np.save(history_path, self.history)
        print(f"✅ 训练历史已保存: {history_path}")

    def generate_visualizations(self) -> None:
        """生成可视化结果"""
        print("\n" + "=" * 60)
        print("步骤5: 生成可视化结果")
        print("=" * 60)
        
        try:
            # 准备可视化数据
            models_data = {
                'CNN': (self.results, self.history)
            }
            
            # 生成可视化
            print("🎨 生成CNN模型可视化结果...")
            generated_files = auto_visualize(
                models_data=models_data,
                experiment_name="CNN_Training",
                save_path=get_current_paths()['figures']
            )
            
            print("✅ 可视化结果生成完成")
            print(f"📊 生成的图表文件数量: {len(generated_files.get('universal', [])) + len(generated_files.get('legacy', []))}")
            
        except Exception as e:
            print(f"❌ 生成可视化时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def generate_report(self) -> None:
        """生成实验报告"""
        print("\n" + "=" * 60)
        print("步骤6: 生成实验报告")
        print("=" * 60)
        
        # 获取模型信息
        model_info = self.model.get_model_info()
        
        # 生成报告内容
        report_content = f"""# CNN模型训练实验报告

## 实验概述
本实验使用卷积神经网络(CNN)进行风电功率预测，验证CNN在时序数据特征提取方面的能力。

## 模型配置
- **模型类型**: {model_info['model_name']}
- **序列长度**: {model_info['sequence_length']}
- **特征数量**: {model_info['n_features']}
- **卷积滤波器**: {model_info['conv_filters']}
- **卷积核大小**: {model_info['conv_kernel_sizes']}
- **池化大小**: {model_info['pool_sizes']}
- **全连接层**: {model_info['dense_units']}
- **Dropout率**: {model_info['dropout_rate']}
- **总参数量**: {model_info['total_params']:,}

## 训练配置
- **训练轮数**: {MODEL_CONFIG['epochs']}
- **学习率**: {MODEL_CONFIG['learning_rate']}
- **批次大小**: {MODEL_CONFIG['batch_size']}
- **早停耐心值**: {MODEL_CONFIG['patience']}

## 性能结果

### 测试集性能
- **RMSE**: {self.results['test']['RMSE']:.6f}
- **R^2**: {self.results['test']['R2']:.6f}
- **MAE**: {self.results['test']['MAE']:.6f}
- **MAPE**: {self.results['test']['MAPE']:.6f}

### 验证集性能
- **RMSE**: {self.results['val']['RMSE']:.6f}
- **R^2**: {self.results['val']['R2']:.6f}
- **MAE**: {self.results['val']['MAE']:.6f}
- **MAPE**: {self.results['val']['MAPE']:.6f}

## 模型特点

### CNN架构优势
- **局部特征提取**: 卷积层能够有效提取时序数据的局部模式
- **参数共享**: 卷积核参数共享减少了模型复杂度
- **平移不变性**: 对时序数据中的模式位置变化具有鲁棒性
- **层次特征学习**: 多层卷积能够学习从低级到高级的特征表示

### 适用场景
- **短期预测**: 适合捕获短期时序模式
- **特征丰富数据**: 在多特征时序数据上表现良好
- **实时预测**: 推理速度快，适合实时应用

## 结论
CNN模型在风电功率预测任务上展现了良好的特征提取能力，特别适合处理具有局部时序模式的数据。

---

**实验时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据集**: {DATASET_CONFIG['data_description']}
"""
        
        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'cnn_training_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 实验报告已保存: {report_path}")

    def run_experiment(self) -> None:
        """运行完整的CNN训练实验"""
        print("开始CNN模型训练实验")
        print("=" * 60)

        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()

            # 2. 创建和训练模型
            self.create_and_train_model(data)

            # 3. 评估模型
            self.evaluate_model(data)

            # 4. 保存模型和结果
            self.save_model_and_results()

            # 5. 生成可视化
            self.generate_visualizations()

            # 6. 生成报告
            self.generate_report()

            print(f"\n🎉 CNN模型训练实验完成！")
            print(f"📁 所有结果已保存到: {get_current_paths()['results_root']}")

        except Exception as e:
            print(f"\n❌ 实验过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    experiment = CNNExperiment()
    experiment.run_experiment()


if __name__ == "__main__":
    main()
