"""
PDF文件阅读器
支持PDF文件的文本提取、页面分析、表格提取等功能
"""

import os
import sys
import re
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False


class PDFReader:
    """PDF文件阅读器类"""
    
    def __init__(self, pdf_path: str):
        """
        初始化PDF阅读器
        
        Args:
            pdf_path: PDF文件路径
        """
        self.pdf_path = Path(pdf_path)
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        self.text_content = ""
        self.pages_text = []
        self.metadata = {}
        self.page_count = 0
        
        # 检查可用的PDF库
        self._check_dependencies()
        
    def _check_dependencies(self):
        """检查PDF处理库的可用性"""
        available_libs = []
        if PYPDF2_AVAILABLE:
            available_libs.append("PyPDF2")
        if PDFPLUMBER_AVAILABLE:
            available_libs.append("pdfplumber")
        if PYMUPDF_AVAILABLE:
            available_libs.append("PyMuPDF")
        if TABULA_AVAILABLE:
            available_libs.append("tabula-py")
            
        if not available_libs:
            raise ImportError(
                "没有找到可用的PDF处理库。请安装以下库之一:\n"
                "pip install PyPDF2\n"
                "pip install pdfplumber\n"
                "pip install PyMuPDF\n"
                "pip install tabula-py"
            )
        
        print(f"可用的PDF处理库: {', '.join(available_libs)}")
        
    def read_with_pypdf2(self) -> str:
        """使用PyPDF2读取PDF文件"""
        if not PYPDF2_AVAILABLE:
            raise ImportError("PyPDF2库未安装")
            
        text_content = ""
        pages_text = []
        
        try:
            with open(self.pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                self.page_count = len(pdf_reader.pages)
                
                # 获取元数据
                if pdf_reader.metadata:
                    self.metadata = {
                        'title': pdf_reader.metadata.get('/Title', ''),
                        'author': pdf_reader.metadata.get('/Author', ''),
                        'subject': pdf_reader.metadata.get('/Subject', ''),
                        'creator': pdf_reader.metadata.get('/Creator', ''),
                        'producer': pdf_reader.metadata.get('/Producer', ''),
                        'creation_date': pdf_reader.metadata.get('/CreationDate', ''),
                        'modification_date': pdf_reader.metadata.get('/ModDate', '')
                    }
                
                # 提取每页文本
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        pages_text.append(page_text)
                        text_content += f"\n--- 第 {page_num + 1} 页 ---\n{page_text}\n"
                    except Exception as e:
                        print(f"警告: 第{page_num + 1}页文本提取失败: {e}")
                        pages_text.append("")
                        
        except Exception as e:
            raise Exception(f"PyPDF2读取PDF失败: {e}")
            
        self.text_content = text_content
        self.pages_text = pages_text
        return text_content
        
    def read_with_pdfplumber(self) -> str:
        """使用pdfplumber读取PDF文件（更好的文本提取）"""
        if not PDFPLUMBER_AVAILABLE:
            raise ImportError("pdfplumber库未安装")
            
        text_content = ""
        pages_text = []
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                self.page_count = len(pdf.pages)
                
                # 获取元数据
                if pdf.metadata:
                    self.metadata = pdf.metadata
                
                # 提取每页文本
                for page_num, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            pages_text.append(page_text)
                            text_content += f"\n--- 第 {page_num + 1} 页 ---\n{page_text}\n"
                        else:
                            pages_text.append("")
                            print(f"警告: 第{page_num + 1}页没有提取到文本")
                    except Exception as e:
                        print(f"警告: 第{page_num + 1}页文本提取失败: {e}")
                        pages_text.append("")
                        
        except Exception as e:
            raise Exception(f"pdfplumber读取PDF失败: {e}")
            
        self.text_content = text_content
        self.pages_text = pages_text
        return text_content
        
    def read_with_pymupdf(self) -> str:
        """使用PyMuPDF读取PDF文件（最快速度）"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF库未安装")
            
        text_content = ""
        pages_text = []
        
        try:
            doc = fitz.open(self.pdf_path)
            self.page_count = doc.page_count
            
            # 获取元数据
            self.metadata = doc.metadata
            
            # 提取每页文本
            for page_num in range(doc.page_count):
                try:
                    page = doc[page_num]
                    page_text = page.get_text()
                    pages_text.append(page_text)
                    text_content += f"\n--- 第 {page_num + 1} 页 ---\n{page_text}\n"
                except Exception as e:
                    print(f"警告: 第{page_num + 1}页文本提取失败: {e}")
                    pages_text.append("")
                    
            doc.close()
            
        except Exception as e:
            raise Exception(f"PyMuPDF读取PDF失败: {e}")
            
        self.text_content = text_content
        self.pages_text = pages_text
        return text_content
        
    def extract_tables_with_tabula(self, page_numbers: Optional[List[int]] = None) -> List[Dict]:
        """使用tabula-py提取表格"""
        if not TABULA_AVAILABLE:
            raise ImportError("tabula-py库未安装")
            
        tables = []
        try:
            if page_numbers is None:
                # 提取所有页面的表格
                dfs = tabula.read_pdf(str(self.pdf_path), pages='all', multiple_tables=True)
            else:
                # 提取指定页面的表格
                pages_str = ','.join(map(str, page_numbers))
                dfs = tabula.read_pdf(str(self.pdf_path), pages=pages_str, multiple_tables=True)
            
            for i, df in enumerate(dfs):
                table_info = {
                    'table_index': i,
                    'shape': df.shape,
                    'columns': df.columns.tolist(),
                    'data': df.to_dict('records')
                }
                tables.append(table_info)
                
        except Exception as e:
            print(f"表格提取失败: {e}")
            
        return tables
        
    def read_pdf(self, method: str = 'auto') -> str:
        """
        读取PDF文件
        
        Args:
            method: 读取方法 ('auto', 'pypdf2', 'pdfplumber', 'pymupdf')
            
        Returns:
            提取的文本内容
        """
        if method == 'auto':
            # 自动选择最佳方法
            if PDFPLUMBER_AVAILABLE:
                method = 'pdfplumber'
            elif PYMUPDF_AVAILABLE:
                method = 'pymupdf'
            elif PYPDF2_AVAILABLE:
                method = 'pypdf2'
            else:
                raise ImportError("没有可用的PDF处理库")
        
        if method == 'pypdf2':
            return self.read_with_pypdf2()
        elif method == 'pdfplumber':
            return self.read_with_pdfplumber()
        elif method == 'pymupdf':
            return self.read_with_pymupdf()
        else:
            raise ValueError(f"不支持的读取方法: {method}")
            
    def get_page_text(self, page_number: int) -> str:
        """获取指定页面的文本"""
        if not self.pages_text:
            self.read_pdf()
            
        if 1 <= page_number <= len(self.pages_text):
            return self.pages_text[page_number - 1]
        else:
            raise ValueError(f"页面号超出范围: {page_number} (总页数: {len(self.pages_text)})")
            
    def search_text(self, keyword: str, case_sensitive: bool = False) -> List[Dict]:
        """在PDF中搜索关键词"""
        if not self.pages_text:
            self.read_pdf()
            
        results = []
        flags = 0 if case_sensitive else re.IGNORECASE
        
        for page_num, page_text in enumerate(self.pages_text, 1):
            matches = re.finditer(re.escape(keyword), page_text, flags)
            for match in matches:
                # 获取上下文
                start = max(0, match.start() - 50)
                end = min(len(page_text), match.end() + 50)
                context = page_text[start:end].strip()
                
                results.append({
                    'page': page_num,
                    'position': match.start(),
                    'context': context,
                    'keyword': keyword
                })
                
        return results
        
    def get_summary(self) -> Dict[str, Any]:
        """获取PDF文件摘要信息"""
        if not self.text_content:
            self.read_pdf()
            
        # 统计信息
        total_chars = len(self.text_content)
        total_words = len(self.text_content.split())
        total_lines = len(self.text_content.split('\n'))
        
        return {
            'file_path': str(self.pdf_path),
            'file_size': self.pdf_path.stat().st_size,
            'page_count': self.page_count,
            'total_characters': total_chars,
            'total_words': total_words,
            'total_lines': total_lines,
            'metadata': self.metadata,
            'has_text': total_chars > 100  # 判断是否包含有效文本
        }
        
    def save_text_to_file(self, output_path: str, encoding: str = 'utf-8'):
        """将提取的文本保存到文件"""
        if not self.text_content:
            self.read_pdf()
            
        with open(output_path, 'w', encoding=encoding) as f:
            f.write(self.text_content)
            
        print(f"文本已保存到: {output_path}")


def main():
    """主函数 - 演示PDF阅读器的使用"""
    import argparse
    
    parser = argparse.ArgumentParser(description='PDF文件阅读器')
    parser.add_argument('pdf_path', help='PDF文件路径')
    parser.add_argument('--method', choices=['auto', 'pypdf2', 'pdfplumber', 'pymupdf'], 
                       default='auto', help='读取方法')
    parser.add_argument('--output', help='输出文本文件路径')
    parser.add_argument('--search', help='搜索关键词')
    parser.add_argument('--page', type=int, help='显示指定页面')
    parser.add_argument('--tables', action='store_true', help='提取表格')
    parser.add_argument('--summary', action='store_true', help='显示摘要信息')
    
    args = parser.parse_args()
    
    try:
        # 创建PDF阅读器
        reader = PDFReader(args.pdf_path)
        
        if args.summary:
            # 显示摘要信息
            summary = reader.get_summary()
            print("PDF文件摘要:")
            print(f"  文件路径: {summary['file_path']}")
            print(f"  文件大小: {summary['file_size']:,} 字节")
            print(f"  页面数量: {summary['page_count']}")
            print(f"  字符总数: {summary['total_characters']:,}")
            print(f"  单词总数: {summary['total_words']:,}")
            print(f"  行数总数: {summary['total_lines']:,}")
            print(f"  包含文本: {'是' if summary['has_text'] else '否'}")
            
            if summary['metadata']:
                print("  元数据:")
                for key, value in summary['metadata'].items():
                    if value:
                        print(f"    {key}: {value}")
            return
        
        if args.page:
            # 显示指定页面
            page_text = reader.get_page_text(args.page)
            print(f"第 {args.page} 页内容:")
            print("-" * 50)
            print(page_text)
            return
            
        # 读取PDF文件
        print(f"正在使用 {args.method} 方法读取PDF...")
        text_content = reader.read_pdf(args.method)
        
        if args.search:
            # 搜索关键词
            results = reader.search_text(args.search)
            print(f"搜索关键词 '{args.search}' 的结果:")
            print(f"找到 {len(results)} 个匹配项:")
            for result in results:
                print(f"  第{result['page']}页: ...{result['context']}...")
        elif args.tables:
            # 提取表格
            if TABULA_AVAILABLE:
                tables = reader.extract_tables_with_tabula()
                print(f"提取到 {len(tables)} 个表格:")
                for table in tables:
                    print(f"  表格 {table['table_index']}: {table['shape'][0]}行 x {table['shape'][1]}列")
            else:
                print("tabula-py库未安装，无法提取表格")
        else:
            # 显示文本内容
            if len(text_content) > 1000:
                print("文本内容（前1000字符）:")
                print(text_content[:1000] + "...")
                print(f"\n总共提取了 {len(text_content)} 个字符")
            else:
                print("文本内容:")
                print(text_content)
        
        # 保存到文件
        if args.output:
            reader.save_text_to_file(args.output)
            
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
