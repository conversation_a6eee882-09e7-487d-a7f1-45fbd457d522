"""
CNN-BiGRU融合模型，用于风电功率预测
结合卷积神经网络的特征提取能力和双向GRU的时序建模能力
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Optional, Dict, Any
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.base_model import BaseTimeSeriesModel
from src.utils.config import DATA_PATHS

# CNN-BiGRU模型配置
CNN_BIGRU_CONFIG = {
    'conv_filters': [64, 32],      # 卷积层滤波器数量
    'conv_kernel_sizes': [3, 3],   # 卷积核大小
    'pool_sizes': [2, 2],          # 池化层大小
    'bigru_units': [64, 32],       # 双向GRU层单元数
    'dense_units': [32, 16],       # 全连接层单元数
    'dropout_rate': 0.3,           # Dropout率
    'bidirectional': True          # 双向标志
}

class CNNBiGRUModel(BaseTimeSeriesModel):
    """CNN-BiGRU融合模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 conv_filters: Optional[List[int]] = None,
                 conv_kernel_sizes: Optional[List[int]] = None,
                 pool_sizes: Optional[List[int]] = None,
                 bigru_units: Optional[List[int]] = None,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.3):
        """
        初始化CNN-BiGRU融合模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            conv_filters: 卷积层滤波器数量列表
            conv_kernel_sizes: 卷积核大小列表
            pool_sizes: 池化层大小列表
            bigru_units: 双向GRU层的单元数列表
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
        """
        super().__init__('CNN-BiGRU', sequence_length, n_features)

        # 使用默认配置或自定义配置
        self.conv_filters = conv_filters if conv_filters is not None else CNN_BIGRU_CONFIG['conv_filters']
        self.conv_kernel_sizes = conv_kernel_sizes if conv_kernel_sizes is not None else CNN_BIGRU_CONFIG['conv_kernel_sizes']
        self.pool_sizes = pool_sizes if pool_sizes is not None else CNN_BIGRU_CONFIG['pool_sizes']
        self.bigru_units = bigru_units if bigru_units is not None else CNN_BIGRU_CONFIG['bigru_units']
        self.dense_units = dense_units if dense_units is not None else CNN_BIGRU_CONFIG['dense_units']
        self.dropout_rate = dropout_rate

        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建CNN-BiGRU融合模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # CNN特征提取层
        self.conv_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        self.conv_batch_norms = nn.ModuleList()
        self.conv_dropouts = nn.ModuleList()

        # 第一个卷积层
        self.conv_layers.append(nn.Conv1d(
            in_channels=self.n_features,
            out_channels=self.conv_filters[0],
            kernel_size=self.conv_kernel_sizes[0],
            padding=self.conv_kernel_sizes[0] // 2
        ))
        self.pool_layers.append(nn.MaxPool1d(kernel_size=self.pool_sizes[0]))
        self.conv_batch_norms.append(nn.BatchNorm1d(self.conv_filters[0]))
        self.conv_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续卷积层
        for i in range(1, len(self.conv_filters)):
            self.conv_layers.append(nn.Conv1d(
                in_channels=self.conv_filters[i-1],
                out_channels=self.conv_filters[i],
                kernel_size=self.conv_kernel_sizes[i],
                padding=self.conv_kernel_sizes[i] // 2
            ))
            self.pool_layers.append(nn.MaxPool1d(kernel_size=self.pool_sizes[i]))
            self.conv_batch_norms.append(nn.BatchNorm1d(self.conv_filters[i]))
            self.conv_dropouts.append(nn.Dropout(self.dropout_rate))

        # 计算CNN输出的序列长度和特征数
        self._calculate_cnn_output_dims()

        # 双向GRU层
        self.bigru_layers = nn.ModuleList()
        self.bigru_batch_norms = nn.ModuleList()
        self.bigru_dropouts = nn.ModuleList()

        # 第一个双向GRU层（输入是CNN的输出特征）
        self.bigru_layers.append(nn.GRU(
            input_size=self.conv_filters[-1],  # CNN最后一层的输出特征数
            hidden_size=self.bigru_units[0],
            batch_first=True,
            bidirectional=True,
            dropout=self.dropout_rate if len(self.bigru_units) > 1 else 0
        ))
        self.bigru_batch_norms.append(nn.BatchNorm1d(self.bigru_units[0] * 2))
        self.bigru_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续双向GRU层
        for i in range(1, len(self.bigru_units)):
            self.bigru_layers.append(nn.GRU(
                input_size=self.bigru_units[i-1] * 2,  # 前一层双向输出
                hidden_size=self.bigru_units[i],
                batch_first=True,
                bidirectional=True,
                dropout=self.dropout_rate if i < len(self.bigru_units) - 1 else 0
            ))
            self.bigru_batch_norms.append(nn.BatchNorm1d(self.bigru_units[i] * 2))
            self.bigru_dropouts.append(nn.Dropout(self.dropout_rate))

        # 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 第一个全连接层（输入是最后一个BiGRU层的输出）
        self.dense_layers.append(nn.Linear(self.bigru_units[-1] * 2, self.dense_units[0]))
        self.dense_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续全连接层
        for i in range(1, len(self.dense_units)):
            self.dense_layers.append(nn.Linear(self.dense_units[i-1], self.dense_units[i]))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))

        # 输出层
        self.output_layer = nn.Linear(self.dense_units[-1], 1)

        print(f"CNN-BiGRU融合模型构建完成:")
        print(f"  CNN层: {len(self.conv_layers)}层, 滤波器: {self.conv_filters}")
        print(f"  BiGRU层: {len(self.bigru_layers)}层, 单元数: {self.bigru_units}")
        print(f"  全连接层: {len(self.dense_layers)}层, 单元数: {self.dense_units}")
        print(f"  CNN输出序列长度: {self.cnn_output_length}")

    def _calculate_cnn_output_dims(self):
        """计算CNN输出的维度"""
        # 创建一个虚拟输入来计算输出大小
        dummy_input = torch.randn(1, self.n_features, self.sequence_length)
        
        with torch.no_grad():
            x = dummy_input
            for conv, pool in zip(self.conv_layers, self.pool_layers):
                x = torch.relu(conv(x))
                x = pool(x)
            
            # CNN输出的序列长度
            self.cnn_output_length = x.size(2)

    def apply_cnn_features(self, x):
        """
        应用CNN进行特征提取
        
        Args:
            x: 输入数据 (batch_size, sequence_length, n_features)
            
        Returns:
            cnn_features: CNN提取的特征 (batch_size, cnn_output_length, conv_filters[-1])
        """
        # 调整输入维度: (batch_size, sequence_length, n_features) -> (batch_size, n_features, sequence_length)
        x = x.transpose(1, 2)

        # CNN特征提取
        for conv, pool, batch_norm, dropout in zip(
            self.conv_layers, self.pool_layers, self.conv_batch_norms, self.conv_dropouts):
            
            x = torch.relu(conv(x))
            x = pool(x)
            x = batch_norm(x)
            x = dropout(x)

        # 调整输出维度: (batch_size, conv_filters[-1], cnn_output_length) -> (batch_size, cnn_output_length, conv_filters[-1])
        x = x.transpose(1, 2)
        
        return x

    def apply_bigru_processing(self, x):
        """
        应用BiGRU进行时序建模
        
        Args:
            x: CNN提取的特征 (batch_size, cnn_output_length, conv_filters[-1])
            
        Returns:
            bigru_output: BiGRU的输出 (batch_size, bigru_units[-1] * 2)
        """
        bigru_out = x
        
        for i, (bigru_layer, batch_norm, dropout) in enumerate(zip(
            self.bigru_layers, self.bigru_batch_norms, self.bigru_dropouts)):
            
            bigru_out, _ = bigru_layer(bigru_out)
            
            # 如果不是最后一个BiGRU层，应用批归一化和dropout
            if i < len(self.bigru_layers) - 1:
                bigru_out = bigru_out.transpose(1, 2)
                bigru_out = batch_norm(bigru_out)
                bigru_out = bigru_out.transpose(1, 2)
                bigru_out = dropout(bigru_out)
        
        # 使用最后一个时间步的输出
        final_output = bigru_out[:, -1, :]
        
        return final_output

    def forward(self, x):
        """
        CNN-BiGRU串联架构的完整前向传播

        处理流程:
        1. CNN特征提取：提取局部时序特征
        2. BiGRU时序建模：基于CNN特征进行双向时序建模
        3. 全连接层处理：将特征映射到预测值

        Args:
            x: 输入风电数据 (batch_size, sequence_length, n_features)

        Returns:
            prediction: 功率预测值 (batch_size, 1)
        """
        # 阶段1: CNN特征提取
        cnn_features = self.apply_cnn_features(x)
        
        # 阶段2: BiGRU时序建模
        bigru_features = self.apply_bigru_processing(cnn_features)
        
        # 阶段3: 全连接层处理
        current_features = bigru_features
        
        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            current_features = torch.relu(dense_layer(current_features))
            current_features = dropout(current_features)
        
        # 阶段4: 输出预测
        prediction = self.output_layer(current_features)
        
        return prediction

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            包含模型信息的字典
        """
        if not hasattr(self, 'conv_layers'):
            return {
                'model_name': self.model_name,
                'sequence_length': self.sequence_length,
                'n_features': self.n_features,
                'status': 'Not built'
            }

        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'model_name': self.model_name,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'conv_filters': self.conv_filters,
            'conv_kernel_sizes': self.conv_kernel_sizes,
            'pool_sizes': self.pool_sizes,
            'bigru_units': self.bigru_units,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'cnn_output_length': getattr(self, 'cnn_output_length', 'Unknown'),
            'total_params': total_params,
            'trainable_params': trainable_params,
            'device': next(self.parameters()).device if list(self.parameters()) else 'Not initialized'
        }


# 测试代码
if __name__ == "__main__":
    # 创建测试数据
    sequence_length = 24
    n_features = 10
    batch_size = 32

    # 创建CNN-BiGRU模型
    cnn_bigru_model = CNNBiGRUModel(sequence_length=sequence_length, n_features=n_features)

    # 获取模型信息
    model_info = cnn_bigru_model.get_model_info()
    print("CNN-BiGRU模型信息:", model_info)

    # 测试前向传播
    test_input = torch.randn(batch_size, sequence_length, n_features)
    output = cnn_bigru_model(test_input)
    print(f"输入形状: {test_input.shape}")
    print(f"输出形状: {output.shape}")
    print("CNN-BiGRU模型测试完成！")
