"""
CNN模型，用于风电功率预测
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Optional, Dict, Any
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.base_model import BaseTimeSeriesModel
from src.utils.config import DATA_PATHS

# CNN模型配置
CNN_CONFIG = {
    'conv_filters': [64, 32],      # 卷积层滤波器数量
    'conv_kernel_sizes': [3, 3],   # 卷积核大小
    'pool_sizes': [2, 2],          # 池化层大小
    'dense_units': [64, 32],       # 全连接层单元数
    'dropout_rate': 0.3,           # Dropout率
    'activation': 'relu'           # 激活函数
}

class CNNModel(BaseTimeSeriesModel):
    """CNN模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 conv_filters: Optional[List[int]] = None,
                 conv_kernel_sizes: Optional[List[int]] = None,
                 pool_sizes: Optional[List[int]] = None,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.3):
        """
        初始化CNN模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            conv_filters: 卷积层滤波器数量列表
            conv_kernel_sizes: 卷积核大小列表
            pool_sizes: 池化层大小列表
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
        """
        super().__init__('CNN', sequence_length, n_features)

        # 使用默认配置或自定义配置
        self.conv_filters = conv_filters if conv_filters is not None else CNN_CONFIG['conv_filters']
        self.conv_kernel_sizes = conv_kernel_sizes if conv_kernel_sizes is not None else CNN_CONFIG['conv_kernel_sizes']
        self.pool_sizes = pool_sizes if pool_sizes is not None else CNN_CONFIG['pool_sizes']
        self.dense_units = dense_units if dense_units is not None else CNN_CONFIG['dense_units']
        self.dropout_rate = dropout_rate

        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建CNN模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # 卷积层
        self.conv_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()

        # 第一个卷积层
        self.conv_layers.append(nn.Conv1d(
            in_channels=self.n_features,
            out_channels=self.conv_filters[0],
            kernel_size=self.conv_kernel_sizes[0],
            padding=self.conv_kernel_sizes[0] // 2
        ))
        self.pool_layers.append(nn.MaxPool1d(kernel_size=self.pool_sizes[0]))
        self.batch_norms.append(nn.BatchNorm1d(self.conv_filters[0]))
        self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续卷积层
        for i in range(1, len(self.conv_filters)):
            self.conv_layers.append(nn.Conv1d(
                in_channels=self.conv_filters[i-1],
                out_channels=self.conv_filters[i],
                kernel_size=self.conv_kernel_sizes[i],
                padding=self.conv_kernel_sizes[i] // 2
            ))
            self.pool_layers.append(nn.MaxPool1d(kernel_size=self.pool_sizes[i]))
            self.batch_norms.append(nn.BatchNorm1d(self.conv_filters[i]))
            self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 计算卷积层输出的特征数量
        self._calculate_conv_output_size()

        # 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 第一个全连接层
        self.dense_layers.append(nn.Linear(self.conv_output_size, self.dense_units[0]))
        self.dense_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续全连接层
        for i in range(1, len(self.dense_units)):
            self.dense_layers.append(nn.Linear(self.dense_units[i-1], self.dense_units[i]))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))

        # 输出层
        self.output_layer = nn.Linear(self.dense_units[-1], 1)

        print(f"CNN模型构建完成:")
        print(f"  卷积层: {len(self.conv_layers)}层, 滤波器: {self.conv_filters}")
        print(f"  全连接层: {len(self.dense_layers)}层, 单元数: {self.dense_units}")
        print(f"  卷积输出特征数: {self.conv_output_size}")

    def _calculate_conv_output_size(self):
        """计算卷积层输出的特征数量"""
        # 创建一个虚拟输入来计算输出大小
        dummy_input = torch.randn(1, self.n_features, self.sequence_length)
        
        with torch.no_grad():
            x = dummy_input
            for conv, pool in zip(self.conv_layers, self.pool_layers):
                x = torch.relu(conv(x))
                x = pool(x)
            
            # 展平后的特征数量
            self.conv_output_size = x.view(1, -1).size(1)

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入数据，形状为 (batch_size, sequence_length, n_features)

        Returns:
            输出预测值，形状为 (batch_size, 1)
        """
        # 调整输入维度: (batch_size, sequence_length, n_features) -> (batch_size, n_features, sequence_length)
        x = x.transpose(1, 2)

        # 卷积层
        for i, (conv, pool, batch_norm, dropout) in enumerate(zip(
            self.conv_layers, self.pool_layers, self.batch_norms, self.dropouts)):
            
            x = torch.relu(conv(x))
            x = pool(x)
            x = batch_norm(x)
            x = dropout(x)

        # 展平
        x = x.view(x.size(0), -1)

        # 全连接层
        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            x = torch.relu(dense_layer(x))
            x = dropout(x)

        # 输出层
        output = self.output_layer(x)

        return output

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            包含模型信息的字典
        """
        if not hasattr(self, 'conv_layers'):
            return {
                'model_name': self.model_name,
                'sequence_length': self.sequence_length,
                'n_features': self.n_features,
                'status': 'Not built'
            }

        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'model_name': self.model_name,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'conv_filters': self.conv_filters,
            'conv_kernel_sizes': self.conv_kernel_sizes,
            'pool_sizes': self.pool_sizes,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'conv_output_size': getattr(self, 'conv_output_size', 'Unknown'),
            'total_params': total_params,
            'trainable_params': trainable_params,
            'device': next(self.parameters()).device if list(self.parameters()) else 'Not initialized'
        }


# 测试代码
if __name__ == "__main__":
    # 创建测试数据
    sequence_length = 24
    n_features = 10
    batch_size = 32

    # 创建CNN模型
    cnn_model = CNNModel(sequence_length=sequence_length, n_features=n_features)

    # 获取模型信息
    model_info = cnn_model.get_model_info()
    print("CNN模型信息:", model_info)

    # 测试前向传播
    test_input = torch.randn(batch_size, sequence_length, n_features)
    output = cnn_model(test_input)
    print(f"输入形状: {test_input.shape}")
    print(f"输出形状: {output.shape}")
    print("CNN模型测试完成！")
