# 深度学习模型全面对比实验

## 🎯 实验概述

本项目实现了一个完整的深度学习模型对比实验框架，用于风电功率预测任务。通过统一的实验接口，可以对比多种不同架构的深度学习模型性能，为实际应用提供模型选择指导。

## 🏗️ 支持的模型架构

### 基础模型
- **Baseline-BiGRU**: 基线双向GRU模型，作为性能对比基准
- **BiLSTM**: 双向LSTM模型，长短期记忆网络的双向版本
- **CNN**: 卷积神经网络，专注于局部特征提取

### 注意力机制模型
- **DPTAM-BiGRU**: 分段时序注意力机制 + 双向GRU
- **ASB-DPTAM-BiGRU**: 自适应频谱块 + 时序注意力 + 双向GRU的串联架构

### 融合架构模型
- **CNN-BiGRU**: CNN特征提取 + BiGRU时序建模的串联架构
- **CNN-BiLSTM**: CNN特征提取 + BiLSTM时序建模的串联架构

## ⚙️ 模型开关配置

在 `src/utils/config.py` 中的 `COMPARISON_EXPERIMENT_CONFIG` 可以控制哪些模型参与对比：

```python
COMPARISON_EXPERIMENT_CONFIG = {
    # 模型开关 - 可以选择性启用/禁用模型
    'enable_baseline_bigru': True,      # 基线BiGRU模型
    'enable_dptam_bigru': True,         # DPTAM-BiGRU模型
    'enable_asb_dptam_bigru': True,     # ASB-DPTAM-BiGRU模型
    'enable_cnn': True,                 # CNN模型
    'enable_cnn_bigru': True,           # CNN-BiGRU模型
    'enable_bilstm': True,              # BiLSTM模型
    'enable_cnn_bilstm': True,          # CNN-BiLSTM模型
}
```

## 🚀 快速开始

### 1. 环境准备
确保已安装所需依赖：
```bash
pip install torch torchvision numpy pandas matplotlib scikit-learn
```

### 2. 测试新模型
首先验证所有模型是否正常工作：
```bash
python test_new_models.py
```

### 3. 运行对比实验
```bash
python run_comprehensive_comparison.py
```

### 4. 查看结果
实验完成后，结果将保存在 `results/时间戳/` 目录下：
- `models/`: 训练好的模型文件
- `figures/`: 可视化图表
- `reports/`: 详细的对比报告

## 📊 实验输出

### 性能对比指标
- **RMSE**: 均方根误差
- **R^2**: 决定系数
- **MAE**: 平均绝对误差
- **MAPE**: 平均绝对百分比误差

### 分析维度
1. **基础性能对比**: 各模型在测试集上的预测精度
2. **参数效率分析**: 模型复杂度与性能的平衡
3. **架构优势分析**: 不同架构的技术特点和适用场景
4. **改进效果量化**: 相对基线模型的性能提升

### 可视化图表
- 性能对比柱状图
- 训练过程曲线
- 预测结果对比
- 参数量对比

## 🔧 自定义配置

### 模型参数配置
每个模型都有独立的配置字典，可以在 `src/utils/config.py` 中调整：

```python
# CNN模型配置
CNN_CONFIG = {
    'conv_filters': [64, 32],          # 卷积层滤波器数量
    'conv_kernel_sizes': [3, 3],       # 卷积核大小
    'pool_sizes': [2, 2],              # 池化层大小
    'dense_units': [64, 32],           # 全连接层单元数
    'dropout_rate': 0.3,               # Dropout率
}

# CNN-BiGRU配置
CNN_BIGRU_CONFIG = {
    'conv_filters': [64, 32],          # CNN部分配置
    'conv_kernel_sizes': [3, 3],
    'pool_sizes': [2, 2],
    'bigru_units': [64, 32],           # BiGRU部分配置
    'dense_units': [32, 16],
    'dropout_rate': 0.3,
}
```

### 训练参数配置
在 `MODEL_CONFIG` 中调整训练相关参数：

```python
MODEL_CONFIG = {
    'sequence_length': 24,             # 时序长度
    'batch_size': 32,                  # 批次大小
    'epochs': 80,                      # 训练轮数
    'learning_rate': 0.0005,           # 学习率
    'patience': 15,                    # 早停耐心值
}
```

## 📈 模型架构详解

### CNN模型
- **特点**: 局部特征提取，参数共享，平移不变性
- **适用**: 短期预测，特征丰富的数据
- **优势**: 计算效率高，适合实时预测

### CNN-BiGRU融合模型
- **架构**: CNN特征提取 → BiGRU时序建模
- **特点**: 结合CNN的局部特征提取和BiGRU的时序建模能力
- **适用**: 需要同时捕获局部模式和长期依赖的场景

### BiLSTM模型
- **特点**: 双向长短期记忆，门控机制，梯度稳定
- **适用**: 长期时序依赖建模
- **优势**: 处理长序列时梯度更稳定

### CNN-BiLSTM融合模型
- **架构**: CNN特征提取 → BiLSTM时序建模
- **特点**: CNN局部特征 + BiLSTM长期记忆
- **适用**: 复杂时序模式，需要长期记忆的预测任务

## 🎯 实验建议

### 模型选择指导
1. **追求最高精度**: 运行全部模型，选择RMSE最低的
2. **平衡精度与效率**: 考虑参数量和推理速度
3. **实时预测场景**: 优先选择CNN等轻量级模型
4. **离线分析场景**: 可选择复杂的融合架构

### 优化策略
1. **超参数调优**: 对最佳模型进行网格搜索或贝叶斯优化
2. **集成学习**: 结合多个表现良好的模型
3. **特征工程**: 基于模型分析结果优化输入特征
4. **架构改进**: 设计新的融合策略

## 📝 实验报告示例

实验完成后会生成详细报告，包含：

```markdown
# 深度学习模型全面对比分析报告

## 性能对比结果
| 模型 | RMSE | R^2 | MAE | 参数量 | 相对基线改进 |
|------|------|----|----|--------|-------------|
| Baseline-BiGRU | 0.045123 | 0.8234 | 0.032145 | 45,321 | 基线 |
| CNN | 0.043567 | 0.8356 | 0.031234 | 25,697 | +3.45% |
| CNN-BiGRU | 0.041234 | 0.8567 | 0.029876 | 82,977 | +8.62% |
| ... | ... | ... | ... | ... | ... |

## 技术洞察
- CNN模型在参数效率方面表现优秀
- 融合架构通常能获得更好的预测精度
- 注意力机制对时序建模有显著帮助
```

## 🔍 故障排除

### 常见问题
1. **OpenMP冲突**: 设置环境变量 `KMP_DUPLICATE_LIB_OK=TRUE`
2. **内存不足**: 减少批次大小或禁用部分模型
3. **CUDA错误**: 检查GPU驱动和PyTorch版本兼容性

### 调试建议
1. 先运行 `test_new_models.py` 验证模型正确性
2. 逐个启用模型进行测试
3. 检查数据文件路径和格式
4. 查看详细错误日志进行定位

## 📚 扩展阅读

- [ASB-DPTAM架构详解](README_ASB_DPTAM_BiGRU.md)
- [串联架构优势分析](串联架构优势分析.md)
- [消融实验说明](ASB-DPTAM优势对比实验更新说明.md)

---

**版本**: 1.0  
**更新时间**: 2025-07-29  
**作者**: Augment Agent
