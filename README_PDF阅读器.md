# PDF文件阅读器

## 🎯 功能概述

这是一个功能完整的PDF文件阅读器，支持多种PDF处理库，提供文本提取、页面分析、关键词搜索、表格提取等功能。

## 🏗️ 主要功能

### 📖 文本提取
- 支持多种PDF处理库（PyPDF2、pdfplumber、PyMuPDF）
- 自动选择最佳提取方法
- 保持页面结构和格式
- 处理加密和复杂布局的PDF

### 🔍 内容分析
- 页面级文本提取
- 关键词搜索和定位
- 文档元数据获取
- 文本统计分析

### 📊 表格处理
- 自动检测和提取表格
- 支持多页表格处理
- 导出为结构化数据
- 表格内容分析

### 💾 数据导出
- 保存为文本文件
- 支持多种编码格式
- 结构化数据导出
- 批量处理支持

## 📦 依赖库安装

### 基础文本提取（选择一个）

```bash
# 推荐：最佳文本提取质量
pip install pdfplumber

# 或者：最快处理速度
pip install PyMuPDF

# 或者：轻量级选择
pip install PyPDF2
```

### 表格提取功能（可选）

```bash
# 表格提取功能
pip install tabula-py

# 注意：tabula-py需要Java环境
# Windows: 下载并安装Java JDK
# macOS: brew install java
# Linux: sudo apt-get install default-jdk
```

### 一键安装所有依赖

```bash
pip install PyPDF2 pdfplumber PyMuPDF tabula-py
```

## 🚀 快速开始

### 1. 基础使用

```python
from src.utils.pdf_reader import PDFReader

# 创建PDF阅读器
reader = PDFReader("your_file.pdf")

# 读取PDF内容
text_content = reader.read_pdf()
print(text_content)

# 获取文档摘要
summary = reader.get_summary()
print(f"页面数: {summary['page_count']}")
print(f"字符数: {summary['total_characters']}")
```

### 2. 分页阅读

```python
# 读取指定页面
page_1_text = reader.get_page_text(1)
print(f"第1页内容: {page_1_text}")

# 遍历所有页面
for i in range(1, reader.page_count + 1):
    page_text = reader.get_page_text(i)
    print(f"第{i}页: {len(page_text)} 字符")
```

### 3. 关键词搜索

```python
# 搜索关键词
results = reader.search_text("深度学习")
for result in results:
    print(f"第{result['page']}页找到: {result['context']}")

# 大小写敏感搜索
results = reader.search_text("AI", case_sensitive=True)
```

### 4. 表格提取

```python
# 提取所有表格
tables = reader.extract_tables_with_tabula()
for table in tables:
    print(f"表格大小: {table['shape']}")
    print(f"列名: {table['columns']}")
    print(f"数据: {table['data'][:3]}")  # 前3行

# 提取指定页面的表格
tables = reader.extract_tables_with_tabula(page_numbers=[1, 2, 3])
```

### 5. 保存文本

```python
# 保存提取的文本
reader.save_text_to_file("extracted_text.txt")

# 指定编码
reader.save_text_to_file("extracted_text.txt", encoding="utf-8")
```

## 🖥️ 命令行使用

### 基础命令

```bash
# 读取PDF并显示内容
python src/utils/pdf_reader.py your_file.pdf

# 显示文档摘要
python src/utils/pdf_reader.py your_file.pdf --summary

# 保存文本到文件
python src/utils/pdf_reader.py your_file.pdf --output extracted.txt
```

### 高级功能

```bash
# 搜索关键词
python src/utils/pdf_reader.py your_file.pdf --search "机器学习"

# 显示指定页面
python src/utils/pdf_reader.py your_file.pdf --page 3

# 提取表格
python src/utils/pdf_reader.py your_file.pdf --tables

# 指定处理方法
python src/utils/pdf_reader.py your_file.pdf --method pdfplumber
```

## 📊 支持的PDF处理库对比

| 库名 | 优势 | 适用场景 | 安装难度 |
|------|------|----------|----------|
| **pdfplumber** | 文本提取质量最佳，支持表格检测 | 复杂布局PDF，需要高质量文本 | 简单 |
| **PyMuPDF** | 处理速度最快，功能全面 | 大文件处理，批量操作 | 简单 |
| **PyPDF2** | 轻量级，兼容性好 | 简单PDF，基础文本提取 | 简单 |
| **tabula-py** | 专业表格提取 | 包含大量表格的PDF | 中等（需要Java） |

## 🔧 高级配置

### 自定义读取方法

```python
# 强制使用特定库
text = reader.read_with_pdfplumber()  # 使用pdfplumber
text = reader.read_with_pymupdf()     # 使用PyMuPDF
text = reader.read_with_pypdf2()      # 使用PyPDF2
```

### 错误处理

```python
try:
    reader = PDFReader("file.pdf")
    text = reader.read_pdf()
except FileNotFoundError:
    print("PDF文件不存在")
except ImportError:
    print("缺少PDF处理库")
except Exception as e:
    print(f"处理失败: {e}")
```

### 批量处理

```python
import os
from pathlib import Path

def batch_process_pdfs(folder_path):
    """批量处理PDF文件"""
    pdf_files = Path(folder_path).glob("*.pdf")
    
    for pdf_file in pdf_files:
        try:
            reader = PDFReader(pdf_file)
            text = reader.read_pdf()
            
            # 保存提取的文本
            output_file = pdf_file.with_suffix('.txt')
            reader.save_text_to_file(output_file)
            
            print(f"✅ 处理完成: {pdf_file.name}")
        except Exception as e:
            print(f"❌ 处理失败 {pdf_file.name}: {e}")

# 使用示例
batch_process_pdfs("./pdf_folder")
```

## 🐛 常见问题

### 1. 文本提取不完整
- **原因**: PDF是扫描版或图片格式
- **解决**: 使用OCR工具（如Tesseract）预处理

### 2. 表格提取失败
- **原因**: 缺少Java环境或tabula-py未安装
- **解决**: 安装Java JDK和tabula-py

### 3. 中文显示乱码
- **原因**: 编码问题
- **解决**: 指定正确的编码格式

### 4. 处理速度慢
- **原因**: 使用了较慢的处理库
- **解决**: 切换到PyMuPDF方法

## 📝 使用示例

运行完整的使用示例：

```bash
python pdf_reader_example.py
```

这将演示所有主要功能的使用方法。

## 🔍 API参考

### PDFReader类

#### 初始化
```python
PDFReader(pdf_path: str)
```

#### 主要方法
- `read_pdf(method='auto')` - 读取PDF文件
- `get_page_text(page_number)` - 获取指定页面文本
- `search_text(keyword, case_sensitive=False)` - 搜索关键词
- `extract_tables_with_tabula(page_numbers=None)` - 提取表格
- `get_summary()` - 获取文档摘要
- `save_text_to_file(output_path, encoding='utf-8')` - 保存文本

## 📄 许可证

本项目采用MIT许可证，可自由使用和修改。

---

**版本**: 1.0  
**更新时间**: 2025-07-29  
**作者**: Augment Agent
