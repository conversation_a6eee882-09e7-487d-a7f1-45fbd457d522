"""
运行深度学习模型全面对比实验的简化脚本
演示如何使用模型开关控制实验
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.experiments.comprehensive_model_comparison import ComprehensiveModelComparator
from src.utils.config import COMPARISON_EXPERIMENT_CONFIG

def print_experiment_info():
    """打印实验信息"""
    print("🚀 深度学习模型全面对比实验")
    print("="*80)
    print("本实验将对比以下深度学习架构在风电功率预测任务上的性能：")
    print()
    
    model_descriptions = {
        'Baseline-BiGRU': '基线双向GRU模型 - 标准的双向循环神经网络',
        'DPTAM-BiGRU': 'DPTAM时序注意力+BiGRU - 加入分段时序注意力机制',
        'ASB-DPTAM-BiGRU': 'ASB频域增强+DPTAM+BiGRU - 频域降噪+时序注意力的串联架构',
        'CNN': '卷积神经网络 - 专注于局部特征提取',
        'CNN-BiGRU': 'CNN+BiGRU融合 - 卷积特征提取+双向时序建模',
        'BiLSTM': '双向LSTM - 长短期记忆网络的双向版本',
        'CNN-BiLSTM': 'CNN+BiLSTM融合 - 卷积特征提取+双向LSTM时序建模'
    }
    
    switches = {
        'Baseline-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_baseline_bigru'],
        'DPTAM-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_dptam_bigru'],
        'ASB-DPTAM-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_asb_dptam_bigru'],
        'CNN': COMPARISON_EXPERIMENT_CONFIG['enable_cnn'],
        'CNN-BiGRU': COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bigru'],
        'BiLSTM': COMPARISON_EXPERIMENT_CONFIG['enable_bilstm'],
        'CNN-BiLSTM': COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bilstm']
    }
    
    enabled_models = []
    disabled_models = []
    
    for model_name, description in model_descriptions.items():
        enabled = switches.get(model_name, False)
        status = "✅ 启用" if enabled else "⏭️ 禁用"
        print(f"{status} {model_name}: {description}")
        
        if enabled:
            enabled_models.append(model_name)
        else:
            disabled_models.append(model_name)
    
    print()
    print(f"📊 实验统计:")
    print(f"   启用模型: {len(enabled_models)}/{len(model_descriptions)} 个")
    print(f"   预计训练时间: {len(enabled_models) * 10}-{len(enabled_models) * 20} 分钟")
    
    if disabled_models:
        print(f"   跳过模型: {', '.join(disabled_models)}")
    
    print()
    print("💡 提示:")
    print("   - 可以在 src/utils/config.py 中的 COMPARISON_EXPERIMENT_CONFIG 修改模型开关")
    print("   - 每个模型的训练结果将保存在独立的时间戳文件夹中")
    print("   - 实验完成后将生成全面的对比报告和可视化图表")
    print("="*80)

def confirm_experiment():
    """确认是否开始实验"""
    enabled_count = sum([
        COMPARISON_EXPERIMENT_CONFIG['enable_baseline_bigru'],
        COMPARISON_EXPERIMENT_CONFIG['enable_dptam_bigru'],
        COMPARISON_EXPERIMENT_CONFIG['enable_asb_dptam_bigru'],
        COMPARISON_EXPERIMENT_CONFIG['enable_cnn'],
        COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bigru'],
        COMPARISON_EXPERIMENT_CONFIG['enable_bilstm'],
        COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bilstm']
    ])
    
    if enabled_count == 0:
        print("⚠️ 警告: 没有启用任何模型，无法进行对比实验")
        print("请在 src/utils/config.py 中启用至少一个模型")
        return False
    
    print(f"🔄 准备训练 {enabled_count} 个模型...")
    
    # 在实际部署中，可以添加用户确认
    # response = input("是否开始实验？(y/n): ")
    # return response.lower() in ['y', 'yes', '是']
    
    # 自动开始（用于演示）
    return True

def run_experiment():
    """运行实验"""
    try:
        # 创建对比实验实例
        comparator = ComprehensiveModelComparator()
        
        # 运行完整对比实验
        comparator.run_comparison_experiment()
        
        print("\n🎉 深度学习模型全面对比实验成功完成！")
        print("\n📋 实验结果包含:")
        print("   ✅ 所有启用模型的训练结果")
        print("   ✅ 详细的性能对比分析")
        print("   ✅ 可视化图表和报告")
        print("   ✅ 模型参数效率分析")
        print("   ✅ 技术洞察和应用建议")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 实验过程中发生错误: {str(e)}")
        print("\n🔧 可能的解决方案:")
        print("   1. 检查数据文件是否存在")
        print("   2. 确认GPU/CPU资源是否充足")
        print("   3. 检查模型配置是否正确")
        print("   4. 查看详细错误信息进行调试")
        
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 设置环境变量解决OpenMP冲突
    os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
    
    # 打印实验信息
    print_experiment_info()
    
    # 确认实验
    if not confirm_experiment():
        print("❌ 实验已取消")
        return False
    
    # 运行实验
    success = run_experiment()
    
    if success:
        print("\n🎯 实验建议:")
        print("   1. 查看生成的对比报告了解各模型性能")
        print("   2. 根据应用场景选择最适合的模型")
        print("   3. 对最佳模型进行进一步的超参数优化")
        print("   4. 考虑集成学习提升预测性能")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
